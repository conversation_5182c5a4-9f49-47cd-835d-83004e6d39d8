using System;
using System.Threading.Tasks;
using Excalibur.Services;
using Microsoft.Extensions.Logging;

namespace Excalibur.Tests
{
    class TestRunner
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("🧪 Executando testes do sistema de Martingale...");
            Console.WriteLine("=".PadRight(50, '='));
            
            try
            {
                // Criar logger mock
                var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
                var logger = loggerFactory.CreateLogger<MartingaleService>();
                
                // Criar instância do serviço
                var martingaleService = new MartingaleService(logger);
                
                // Executar teste específico da sequência
                await TestMartingaleSequence(martingaleService);
                
                Console.WriteLine("\n✅ Todos os testes foram executados!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erro durante os testes: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("\nPressione qualquer tecla para sair...");
            Console.ReadKey();
        }
        
        static async Task TestMartingaleSequence(MartingaleService martingaleService)
        {
            Console.WriteLine("\n--- Teste: Sequência de Martingale Corrigida ---");
            
            decimal baseStake = 0.35m;
            decimal factor = 2.1m;
            int maxLevel = 8;
            
            // Inicializar sistema
            martingaleService.InitializeMartingaleSettings(baseStake, factor, maxLevel);
            
            Console.WriteLine($"Configuração: BaseStake={baseStake}, Factor={factor}, MaxLevel={maxLevel}");
            Console.WriteLine("Sequência esperada (CORRIGIDA):");
            Console.WriteLine("Entrada 1 (nível 1): 0.35000000 (stake base)");
            Console.WriteLine("Após perda → Entrada 2 (nível 2): 0.73500000 (0.35 × 2.1^1)");
            Console.WriteLine("Após perda → Entrada 3 (nível 3): 1.54350000 (0.35 × 2.1^2)");
            Console.WriteLine("Após perda → Entrada 4 (nível 4): 3.24135000 (0.35 × 2.1^3)");
            Console.WriteLine("Após perda → Entrada 5 (nível 5): 6.80683500 (0.35 × 2.1^4)");
            Console.WriteLine("Após perda → Entrada 6 (nível 6): 14.29435350 (0.35 × 2.1^5)");
            Console.WriteLine();
            
            // Valores esperados exatos
            decimal[] expectedStakes = {
                0.35m,          // Nível 1: 0.35 × 2.1^0 = 0.35
                0.735m,         // Nível 2: 0.35 × 2.1^1 = 0.735
                1.5435m,        // Nível 3: 0.35 × 2.1^2 = 1.5435
                3.24135m,       // Nível 4: 0.35 × 2.1^3 = 3.24135
                6.806835m,      // Nível 5: 0.35 × 2.1^4 = 6.806835
                14.2943535m,    // Nível 6: 0.35 × 2.1^5 = 14.2943535
                30.01814235m,   // Nível 7: 0.35 × 2.1^6 = 30.01814235
                63.038298935m   // Nível 8: 0.35 × 2.1^7 = 63.038298935
            };
            
            // Verificar estado inicial
            var initialLevel = martingaleService.GetCurrentMartingaleLevel();
            var initialStake = martingaleService.GetCurrentStakeForNewEntry();
            Console.WriteLine($"Estado inicial - Level: {initialLevel}, Stake: {initialStake:F8}");
            
            if (Math.Abs(initialStake - expectedStakes[0]) > 0.00000001m)
            {
                Console.WriteLine($"❌ ERRO: Stake inicial incorreto! Esperado: {expectedStakes[0]:F8}, Atual: {initialStake:F8}");
                return;
            }
            Console.WriteLine("✅ Stake inicial correto");
            
            // Simular perdas seguidas
            for (int i = 1; i <= 6; i++)
            {
                Console.WriteLine($"\n--- Processando perda #{i} ---");
                
                // Processar perda
                martingaleService.ProcessContractResult(false, $"mock_contract_loss_{i}");
                
                // Estado após a perda
                var levelAfter = martingaleService.GetCurrentMartingaleLevel();
                var stakeAfter = martingaleService.GetCurrentStakeForNewEntry();
                var expectedStake = expectedStakes[levelAfter - 1];
                
                Console.WriteLine($"Após perda #{i}:");
                Console.WriteLine($"  Level: {levelAfter}");
                Console.WriteLine($"  Stake calculado: {stakeAfter:F8}");
                Console.WriteLine($"  Stake esperado:  {expectedStake:F8}");
                Console.WriteLine($"  Diferença:       {Math.Abs(stakeAfter - expectedStake):F8}");
                
                // Verificar precisão
                if (Math.Abs(stakeAfter - expectedStake) > 0.00000001m)
                {
                    Console.WriteLine($"❌ ERRO: Stake incorreto no nível {levelAfter}!");
                    Console.WriteLine($"   Esperado: {expectedStake:F8}");
                    Console.WriteLine($"   Obtido:   {stakeAfter:F8}");
                    return;
                }
                else
                {
                    Console.WriteLine($"✅ Stake correto para nível {levelAfter}");
                }
            }
            
            // Testar reset após vitória
            Console.WriteLine("\n--- Testando reset após vitória ---");
            martingaleService.ProcessContractResult(true, "mock_contract_win");
            var finalLevel = martingaleService.GetCurrentMartingaleLevel();
            var finalStake = martingaleService.GetCurrentStakeForNewEntry();
            
            Console.WriteLine($"Após vitória - Level: {finalLevel}, Stake: {finalStake:F8}");
            
            if (finalLevel != 1 || Math.Abs(finalStake - baseStake) > 0.00000001m)
            {
                Console.WriteLine($"❌ ERRO: Reset após vitória falhou!");
                Console.WriteLine($"   Esperado: Level=1, Stake={baseStake:F8}");
                Console.WriteLine($"   Obtido:   Level={finalLevel}, Stake={finalStake:F8}");
            }
            else
            {
                Console.WriteLine("✅ Reset após vitória funcionou corretamente");
            }
            
            Console.WriteLine("\n🎉 Teste da sequência de martingale concluído com sucesso!");
        }
    }
}
