# Correção do Total P/L na Simulação

## Problema Identificado
A tabela de simulação estava mostrando valores incorretos na coluna "Total P/L" devido a um cálculo errado para contratos binários.

### Exemplo do Bug:
- **Stake**: 0.35 (correto)
- **Start Spot**: 100286.726
- **End Spot**: 100311.396
- **Diferença**: 24.67
- **Total P/L Incorreto**: 24.67 × 0.35 = **8.63** ❌

### Como Deveria Ser:
Para contratos binários, o Total P/L é fixo:
- **Se ganhou**: Payout - Stake (ex: 0.66 - 0.35 = **0.31**) ✅
- **Se perdeu**: -Stake (ex: **-0.35**) ✅

## Causa do Problema
O código estava usando a fórmula de contratos CFD/Forex:
```csharp
// INCORRETO para contratos binários
calculatedPl = priceDifference * transaction.Stake;
```

## Correção Aplicada

### Antes (Incorreto):
```csharp
decimal priceDifference = currentSpot - transaction.StartSpot;
decimal calculatedPl;
if (isDescending)
{
    calculatedPl = -priceDifference * transaction.Stake;
}
else
{
    calculatedPl = priceDifference * transaction.Stake;
}
transaction.TotalProfitLoss = calculatedPl;
```

### Depois (Correto):
```csharp
bool isCurrentlyWinning = transaction.IsCurrentlyWinning(currentSpot);

if (isCurrentlyWinning)
{
    // Se está ganhando: Payout - Stake
    transaction.TotalProfitLoss = transaction.Payout - transaction.Stake;
}
else
{
    // Se está perdendo: -Stake
    transaction.TotalProfitLoss = -transaction.Stake;
}
```

## Arquivos Corrigidos
1. **SimulationViewModel.cs** - Linha 170-187
2. **PurchaseViewModel.cs** - Linha 246-262

## Resultado Esperado
Agora a simulação deve mostrar valores corretos:
- **Vitórias**: +0.31 (para payout 0.66 e stake 0.35)
- **Perdas**: -0.35
- **Sem mais valores altos como 8.63**

## Observação Importante
A simulação **não aplica martingale** no stake (correto), apenas no cálculo do Total P/L que agora está corrigido para contratos binários.
